<?php

// Start session if not already started

if (session_status() === PHP_SESSION_NONE) {

    session_start();

}



// Check if user is logged in

if (!isset($_SESSION['ggc_user_id'])) {

    wp_redirect(home_url('/ggc-login/'));

    exit;

}

?>



<style>

.registration-details {

    background: #fff;

    padding: 30px;

    border-radius: 10px;

    box-shadow: 0 0 20px rgba(0,0,0,0.1);

    margin-bottom: 30px;

}



.registration-details h3 {

    color: #333;

    margin-bottom: 20px;

    padding-bottom: 10px;

    border-bottom: 2px solid #f0f0f0;

}



.registration-details p {

    margin-bottom: 10px;

    line-height: 1.6;

}



.registration-details strong {

    color: #555;

}



.registration-details ul li {

    margin-bottom: 8px;

    color: #666;

}



.registration-details .btn-primary {

    padding: 8px 20px;

}



.registration-details img {

    border-radius: 5px;

    box-shadow: 0 0 10px rgba(0,0,0,0.1);

}

</style>



<!-- leads area -->

<div class="about-area py-120">

    <div class="container">

        <div class="row">

            <div class="col-12">

                <div class="site-heading mb-5">

                    <div class="d-flex justify-content-between align-items-center">

                        <div>

                            <span class="site-title-tagline">Registration Details</span>

                            <h2 class="site-title">

                                Participant <span>Information</span>

                            </h2>

                            <div class="mt-4">

                                <a href="/ggc-register-members" class="theme-btn"><i class="far fa-arrow-left"></i> Back to Members</a>

                            </div>

                        </div>

                        <div>

                            <a href="<?php echo home_url('/ggc-logout/'); ?>" class="theme-btn"><i class="far fa-sign-out"></i> Logout</a>

                        </div>

                    </div>

                </div>

                <?php

                global $wpdb;

                $table_name = $wpdb->prefix . 'user_registration';

                $id = isset($_GET['id']) ? intval($_GET['id']) : 0;

                if (!$id) {

                    wp_die('Invalid request');

                }

                $results = $wpdb->get_results($wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id));

                

                if ($results) :

                ?>

                <?php foreach ($results as $row) : ?>

                <div class="registration-details wow fadeInUp" data-wow-delay=".25s">

                    <h3>Personal Information</h3>

                    <div class="row g-3 mb-4">

                        <div class="col-md-6">

                            <p><strong>First Name:</strong> <?php echo esc_html($row->first_name); ?></p>

                            <p><strong>Last Name:</strong> <?php echo esc_html($row->last_name); ?></p>

                            <p><strong>Phone:</strong> <?php echo esc_html($row->contact_phone); ?></p>

                            <p><strong>Preferred Language:</strong> <?php echo esc_html($row->preferred_language); ?></p>

                        </div>

                        <div class="col-md-6">

                            <p><strong>Country:</strong> <?php echo esc_html($row->country); ?></p>

                            <p><strong>City/State/ZIP:</strong> <?php echo esc_html($row->city_state_zip); ?></p>

                            <p><strong>Participated Before:</strong> <?php echo $row->participated_before ? 'Yes' : 'No'; ?></p>

                        </div>

                    </div>



                    <h3>Business Information</h3>

                    <div class="row g-3 mb-4">

                        <div class="col-md-6">

                            <p><strong>Company Name:</strong> <?php echo esc_html($row->company_name); ?></p>

                            <p><strong>Company Website:</strong> <a href="<?php echo esc_url($row->company_website); ?>" target="_blank"><?php echo esc_html($row->company_website); ?></a></p>

                            <p><strong>LinkedIn:</strong> <a href="<?php echo esc_url($row->linkedin_url); ?>" target="_blank"><?php echo esc_html($row->linkedin_url); ?></a></p>

                            <p><strong>Social Media:</strong> <?php echo esc_html($row->social_media_links); ?></p>

                        </div>

                        <div class="col-md-6">

                            <p><strong>Years in Operation:</strong> <?php echo esc_html($row->years_in_operation); ?></p>

                            <p><strong>Number of Employees:</strong> <?php echo esc_html($row->number_of_employees); ?></p>

                            <p><strong>Annual Revenue:</strong> <?php echo esc_html($row->annual_revenue); ?></p>

                        </div>

                        <div class="col-12">

                            <p><strong>Business Description:</strong></p>

                            <p><?php echo esc_html($row->business_description); ?></p>

                        </div>

                    </div>



                    <h3>Representative Attending</h3>

                    <div class="row g-3 mb-4">

                        <div class="col-md-6">

                            <p><strong>Name:</strong> <?php echo esc_html($row->rep_name); ?></p>

                            <p><strong>Title:</strong> <?php echo esc_html($row->rep_title); ?></p>

                        </div>

                        <div class="col-md-6">

                            <p><strong>Email:</strong> <?php echo esc_html($row->rep_email); ?></p>

                            <p><strong>Phone:</strong> <?php echo esc_html($row->rep_phone); ?></p>

                        </div>

                    </div>



                    <h3>Program Participation</h3>

                    <div class="row g-3 mb-4">

                        <div class="col-12">

                            <p><strong>Attending Days:</strong></p>

                            <ul class="list-unstyled">

                                <?php if ($row->attending_day1) : ?>

                                    <li>✓ Day 1 - Welcome Reception (6:00 PM - 10:00 PM)</li>

                                <?php endif; ?>

                                <?php if ($row->attending_day2) : ?>

                                    <li>✓ Day 2 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)</li>

                                <?php endif; ?>

                                <?php if ($row->attending_day3) : ?>

                                    <li>✓ Day 3 - B2B Matchmaking Sessions (9:00 AM - 5:00 PM)</li>

                                <?php endif; ?>

                                <?php if ($row->attending_day4_visits) : ?>

                                    <li>✓ Day 4 - Site Visits (International Attendees ONLY)</li>

                                <?php endif; ?>

                                <?php if ($row->attending_day4_closing) : ?>

                                    <li>✓ Day 4 - Closing Reception (5:00 PM - 9:00 PM)</li>

                                <?php endif; ?>

                            </ul>

                        </div>

                    </div>



                    <h3>Uploads</h3>

                    <div class="row g-3 mb-4">

                        <div class="col-md-4">

                            <p><strong>Business Profile:</strong></p>

                            <?php if ($row->business_profile_upload) : ?>

                                <a href="<?php echo esc_url($row->business_profile_upload); ?>" target="_blank" class="btn btn-primary">View Business Profile</a>

                            <?php else : ?>

                                <p>Not uploaded</p>

                            <?php endif; ?>

                        </div>

                        <div class="col-md-4">

                            <p><strong>Company Logo:</strong></p>

                            <?php if ($row->company_logo_upload) : ?>

                                <img src="<?php echo esc_url($row->company_logo_upload); ?>" alt="Company Logo" class="img-fluid" style="max-height: 100px;">

                            <?php else : ?>

                                <p>Not uploaded</p>

                            <?php endif; ?>

                        </div>

                        <div class="col-md-4">

                            <p><strong>Representative Headshot:</strong></p>

                            <?php if ($row->headshot_upload) : ?>

                                <img src="<?php echo esc_url($row->headshot_upload); ?>" alt="Representative Headshot" class="img-fluid" style="max-height: 100px;">

                            <?php else : ?>

                                <p>Not uploaded</p>

                            <?php endif; ?>

                        </div>

                    </div>

                </div>

                <?php endforeach; ?>

                <?php else : ?>

                <div class="alert alert-info" role="alert">

                    No registrations found.

                </div>

                <?php endif; ?>

            </div>

        </div>

    </div>

</div>

<!-- leads area end -->