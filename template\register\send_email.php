<?php

// Check if user is logged in
if (empty($_SESSION['ggc_admin'])) {
        wp_redirect(home_url('/ggc-login/'));
        exit;
}

$id = $_GET['id'];
if (empty($id)) {
        wp_redirect(home_url('/ggc-register-members/'));
        exit;
}

global $wpdb;
$table_name = 'user_registration';

// Join user_registration with user_payments using client_id
$query = $wpdb->prepare("
    SELECT *
    FROM $table_name 
    WHERE client_id = %s
", $id);
$row = $wpdb->get_row($query);

if (!empty($row)) {

        $email = $row->email;
        $password = $row->password;

        $message = "
Hello,<br><br>

You will need to complete a B2B matchmaking profile in order to complete the registration process.<br><br>

<strong style='color:red;'>You are not fully registered until you complete the B2B matchmaking profile.</strong><br><br>

PLEASE READ THE FOLLOWING CAREFULLY. <br><br>

An account has been successfully created for you.<br><br>

Here are your <strong>LOGIN DETAILS</strong>:<br>
<strong>Email:</strong> {$email}<br>
<strong>Password:</strong> {$password}<br><br>

Log in here using the following links below, to complete or update your business profile:<br>
<a href='https://goglobalconference.com/ggc-login/'>https://goglobalconference.com/ggc-login/</a><br><br>


If you have any questions, feel free to reach out.<br><br>
Email: <a href='mailto:<EMAIL>'><EMAIL></a><br>
Webpage: <a href='https://goglobalconference.com/'>GoGlobalConference.com/</a><br>

Best regards,<br>
Go Global Conference Team
";


        // Send email
        $to = $email;
        $subject = 'Welcome to Go Global Conference!';
        $message = $message;
        $headers = array(
                'From: Go Global Conference <<EMAIL>>',
                'Cc: <EMAIL>',
                'Content-Type: text/html; charset=UTF-8'
        );

        wp_mail($to, $subject, $message, $headers);
        echo '<div class="container">';
        echo "<br><br><div class='alert alert-success'>Email sent successfully!</div><br><hr><br>";
        echo "To: " . $email . "<br>";
        echo "Subject: " . $subject . "<br>";
        echo "Message: " . $message . "<br><hr><br>";
        echo "<a href='/ggc-register-members/' class='theme-btn btn-admin'>Back to Members</a><br><br>";
        echo '</div>';
} else {
        echo '<div class="container">';
        echo "<br><br><div class='alert alert-danger'>No user found.</div>";
        echo "<a href='/ggc-register-members/' class='theme-btn btn-admin'>Back to Members</a><br><br>";
        echo '</div>';
}
