<?php

/**
 * Template Name: Stripe Webhook Endpoint
 * Description: Receives Stripe webhook events
 */

// Disable theme rendering and send plain response
header('Content-Type: application/json');

function password_generate()
{
    $digits = '';
    for ($i = 0; $i < 5; $i++) {
        $digits .= mt_rand(1, 9);
    }
    return $digits;
}

require_once get_template_directory() . '/vendor/autoload.php'; // Path to stripe-php

// Get the home URL
$home_url = home_url(); // e.g., https://example.com

// Parse the domain from the URL
$parsed_url = parse_url($home_url, PHP_URL_HOST); // e.g., example.com

// Check the domain
if ($parsed_url === 'goglobalconference.com') {
    // Live
    $stripe_secret = '***********************************************************************************************************';
    $webhook_secret = 'whsec_kTk9ztSjLkQx66TYDJu1euEBRlP3wFl9';
} else {
    // Test
    $stripe_secret = 'sk_test_51OZfYnEZ5LMR9wTweebFTo7dtAGvfMfSelFaiPjsIUeNeBhmItHswoHRsKCfzsgfNpw61TgoHPRjibgwcezXhOLy00Tg3YIoIe';
    $webhook_secret = 'whsec_iKuPMhHGuriHoxgtDajXnzW8xRp4fGbz';
}


\Stripe\Stripe::setApiKey($stripe_secret);

// Get the raw payload
$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? null;

try {
    $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $webhook_secret);
    $event_array = $event->jsonSerialize(); // Full payload for optional logging

    // Extract data only for successful checkout
    if ($event->type === 'checkout.session.completed') {
        $session = $event->data->object;

        // Get customer details (email, name, phone)
        $client_reference_id = $session->client_reference_id ?? '';
        $email = $session->customer_details->email ?? '';
        $name  = $session->customer_details->name ?? '';
        $phone = $session->customer_details->phone ?? '';
        $created_at = $session->created ?? '';
        $stripe_customer_id = $session->customer ?? '';

        // Get payment amount by retrieving the PaymentIntent
        $payment_intent_id = $session->payment_intent ?? null;
        $amount = 0;

        if ($payment_intent_id) {
            $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
            $amount = $payment_intent->amount_received / 100; // convert from cents
            $currency = strtoupper($payment_intent->currency);
        }

        // Fetch the line items for this session
        $line_items = \Stripe\Checkout\Session::allLineItems($session->id, ['limit' => 100]);

        foreach ($line_items->data as $item) {
            $product_name = $item->description;
            $quantity = $item->quantity;

            // Do something with the product name and quantity
            error_log("Product: $product_name, Quantity: $quantity");
        }

        // Insert into database
        global $wpdb;
        $table_name = 'user_payments';

        $data = array(
            'client_id' => $client_reference_id,
            'created_at' => $created_at,
            'product_name' => $product_name,
            'quantity' => $quantity,
            'amount'  => $amount,
            'currency' => $currency ?? '',
            'pay_email'   => $email,
            'pay_name'    => $name,
            'pay_phone'   => $phone,
            'stripe_customer_id' => $stripe_customer_id,
            'payload' => json_encode($event_array)
        );

        $format = array('%s', '%s', '%s', '%s', '%s', '%s');
        $result = $wpdb->insert($table_name, $data, $format);

        // -----------------------------------
        if ($product_name !== 'GGC Welcome Reception Pass Only') {

            $table_name = 'user_registration';
            $password = password_generate();

            $data = array(
                'client_id' => $client_reference_id,
                'email'   => $email,
                'password' => $password,
                'contact_phone' => $phone
            );

            $format = array('%s', '%s', '%s');
            $result = $wpdb->insert($table_name, $data, $format);

            $subject = 'Welcome to Go Global Conference!';

            $message = "
            Hello {$name},<br><br>

            Thank you for your payment for the <strong>Go Global Conference</strong>. <br><br>
            

            
            PLEASE READ THE FOLLOWING CAREFULLY. <br><br>

            An account has been successfully created for you.<br><br>

            Here are your <strong>LOGIN DETAILS</strong>:<br>
            <strong>Email:</strong> {$email}<br>
            <strong>Password:</strong> {$password}<br><br>

            Log in here using the following links below, to complete or update your business profile:<br>
            <a href='https://goglobalconference.com/ggc-login/'>https://goglobalconference.com/ggc-login/</a><br><br>


            If you have any questions, feel free to reach out.<br><br>
            Email: <a href='mailto:<EMAIL>'><EMAIL></a><br>
            Webpage: <a href='https://goglobalconference.com/'>GoGlobalConference.com/</a><br>

            Best regards,<br>
            Go Global Conference Team
            ";

        } else {

            $message = "
            Hello {$name},<br><br>

            Thank you for your payment for the <strong>Go Global Conference Welcome Reception</strong>. <br><br>
            
            If you have any questions, feel free to reach out.<br><br>
            Email: <a href='mailto:<EMAIL>'><EMAIL></a><br>
            Webpage: <a href='https://goglobalconference.com/'>GoGlobalConference.com/</a><br>

            Best regards,<br>
            Go Global Conference Team
            ";
            
            $subject = 'Welcome to Go Global Conference Welcome Reception!';
        }

        $headers = array('Content-Type: text/html; charset=UTF-8');
        $headers = array(
            'From: Go Global Conference <<EMAIL>>',
            'Cc: <EMAIL>',
            'Content-Type: text/html; charset=UTF-8'
        );

        wp_mail($email, $subject, $message, $headers);

    }

} catch (\Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid webhook: ' . $e->getMessage()]);
    exit;
}

http_response_code(200);
echo json_encode(['status' => 'success']);
exit;
