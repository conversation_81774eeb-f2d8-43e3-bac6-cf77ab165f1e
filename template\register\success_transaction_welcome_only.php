<style>
    .header {
        display: none;
    }
    .ecom-res td {
        font-size: 12px !important;
    }
</style>
<div class="container">
    <div class="row">
        <div class="col-lg-12">
            <div class="site-heading text-center wow fadeInDown" data-wow-delay=".25s" style="margin-top: 20px; margin-bottom: 0px; visibility: visible; animation-delay: 0.25s; animation-name: fadeInDown;">
                <span class="site-title-tagline">Go Global Conference Welcome Reception</span>
                <h2 class="site-title">October 1st at 6:00 PM - 9:00 PM</h2>
            </div>
        </div>
        <div class="col-lg-12">
            <div class="success-transaction" style="margin-top: 0px; margin-bottom: 0px;">
                <div>
                    <p style="margin: 0;"><b style="color: #c1033f; font-size: 30px; font-weight: normal;">Your payment was successful for the Welcome Reception Pass.</b></p>
                    <p style="margin: 0;">Thank you for your payment. We look forward to seeing you at the Welcome Reception.</p>
                </div>
            </div>
        </div>
        <div class="col-lg-12">
            <?php
            global $wpdb;

            // Table names (automatically add prefix if needed)
            $payments_table = 'user_payments';

            
            // Get the current user ID from session
            if (!empty($_SESSION['ggc_user_id'])) {
                $user_id = $_SESSION['ggc_user_id'];
                setcookie("unique_id", $user_id, time() + 86400, "/", "", true, true);
            } else {
                if (!empty($_COOKIE['unique_id'])) {
                    $user_id = $_COOKIE['unique_id'];
                } else {
                    $user_id = '';
                }
            }
            

            if (!empty($user_id)) {
                // Safely prepare the query
                $query = $wpdb->prepare(
                    "SELECT * FROM $payments_table WHERE client_id = %s",
                    $user_id
                );

                $results = $wpdb->get_row($query);
            }
            if (!empty($results)) { 
            ?>
            <div class="table-thankyou" style="margin-top: 0px; margin-bottom: 0px;">
                <table class="ecom-res">
                    <tr>
                        <td>
                            Name: <b><?= $results->pay_name; ?></b>
                        </td>
                        <td>
                            Email: <b><?= $results->pay_email; ?></b>
                        </td>
                        <td>
                            Phone: <b><?= $results->pay_phone; ?></b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Ticket: <b><?= $results->product_name; ?></b>
                        </td>
                        <td>
                            Quantity: <b><?= $results->quantity; ?></b>
                        </td>
                        <td>
                            Total: <b><?= $results->amount; ?> <?= $results->currency; ?></b>
                        </td>
                    </tr>
                </table>
            </div>
            <?php } ?>
        </div>
    </div>
</div>
<hr>