<?php
// Step 0: Set credentials and config
$clientId = '3_4eqwsqat994w88ss848wssocg0g88sgkc00c8s8884sow04kcc';
$clientSecret = '4e1l6l603h2ckscg0c00gg00o4kkgsocgo0cc8woowk4sgsw80';
$mauticBaseUrl = 'https://crm.goglobalconference.com';

$segmentMauticPaymentRecieved = 114;
$segmentId = $segmentMauticPaymentRecieved;

// Step 1: Get Access Token
$tokenUrl = $mauticBaseUrl . '/oauth/v2/token';
$postFields = [
    'grant_type' => 'client_credentials',
    'client_id' => $clientId,
    'client_secret' => $clientSecret,
];

$ch = curl_init($tokenUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postFields));
$tokenResponse = curl_exec($ch);
curl_close($ch);

$tokenData = json_decode($tokenResponse, true);
$accessToken = $tokenData['access_token'];

// Get the current user ID from session
$user_id = $_SESSION['ggc_user_id'];

// Gathering User Information
if (!empty($user_id)) {

    global $wpdb;

    $query = $wpdb->prepare("
        SELECT *
        FROM user_payments
        WHERE client_id = %s
    ", $user_id);

    $result = $wpdb->get_row($query); // returns single object row

    if ($result) {
        // Split by spaces
        $parts = explode(" ", $result->pay_name);
        // First name = first element
        $firstName = array_shift($parts);
        // Last name = everything else joined back
        $lastName = implode(" ", $parts);

        $contactData = [
            'email' => $result->pay_email,
            'firstname' => $first_name,
            'lastname' => $last_name,
            'phone' => $result->pay_phone
        ];
    } else {
        die('No payment record found for this user.');
    }
} else {
    die('No user found.');
}

// Sending information to Mautic
try {
    // Contact is Created
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . '/api/contacts/new');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($contactData));
    $contactResponse = curl_exec($ch);
    $contactData = json_decode($contactResponse, true);

    curl_close($ch);

    $contactId = $contactData['contact']['id'];

    // Add to segment POST /segments/SEGMENT_ID/contact/CONTACT_ID/add
    $post_uri_segment = "/api/segments/$segmentId/contact/$contactId/add";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . $post_uri_segment);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    $segmentResponse = curl_exec($ch);
    $segmentResponse = json_decode($segmentResponse, true);

    curl_close($ch);

} catch (Exception $e) {
    //die('Failed to create contact.');
}
