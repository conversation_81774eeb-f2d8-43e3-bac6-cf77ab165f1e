<!-- Redirected from Stripe -->
<?php get_header(); ?>

<main class="main">
    <div class="site-breadcrumb" style="background: url(<?php echo get_template_directory_uri(); ?>/assets/img/breadcrumb/01.jpg)">
        <div class="container">
            <h2 class="breadcrumb-title"><?php the_title(); ?></h2>
            <ul class="breadcrumb-menu">
                <li><a href="/">Home</a></li>
                <li class="active"><?php the_title(); ?></li>
            </ul>
        </div>
    </div>
    <?php
        global $wpdb;

        // Table names (automatically add prefix if needed)
        $payments_table = 'user_payments';
        
        // Get the current user ID from session
        if (!empty($_SESSION['ggc_user_id'])) {
            $user_id = $_SESSION['ggc_user_id'];
            setcookie("unique_id", $user_id, time() + 86400, "/", "", true, true);
        } else {
            if (!empty($_COOKIE['unique_id'])) {
                $user_id = $_COOKIE['unique_id'];
            } else {
                $user_id = '';
            }
        }
        
        if (!empty($user_id)) {
            // Safely prepare the query
            $query = $wpdb->prepare(
                "SELECT * FROM $payments_table WHERE client_id = %s",
                $user_id
            );

            $results = $wpdb->get_row($query);
            $_SESSION['ggc_user_id'] = $results->client_id;
            $product_name = $results->product_name;
        }
    ?>
    <?php if ($product_name == 'GGC Welcome Reception Pass Only') : ?>

        <?php get_template_part('template/register/success_transaction_welcome_only'); ?>

        <?php
        echo '<pre>';
        var_dump($_SESSION['ggc_user_id']);
        echo '</pre>';
        get_template_part('template/register/submit_mautic_welcome_reception');
        ?>
    <?php else : ?>
        <?php get_template_part('template/register/process'); ?>
        <?php get_template_part('template/register/success_transaction2'); ?>

        <?php
        set_query_var('call_origin', 'registration');
        get_template_part('template/register/form');
        ?>
        
        <?php
        set_query_var('call_origin', 'paid');
        get_template_part('template/register/submit_mautic');
        ?>
    <?php endif; ?>
</main>

<?php get_footer(); ?>