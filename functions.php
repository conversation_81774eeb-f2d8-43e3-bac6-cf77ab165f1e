<?php

// Theme setup
function mytheme_setup()
{
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ));
    register_nav_menu('main-menu', __('Main Menu'));
}
add_action('after_setup_theme', 'mytheme_setup');

// Enqueue styles and scripts
function mytheme_enqueue_scripts()
{
    // Styles
    wp_enqueue_style('bootstrap', get_template_directory_uri() . '/assets/css/bootstrap.min.css');
    //wp_enqueue_style('fontawesome', get_template_directory_uri() . '/assets/css/all-fontawesome.min.css');
    wp_enqueue_style('animate', get_template_directory_uri() . '/assets/css/animate.min.css');
    wp_enqueue_style('magnific-popup', get_template_directory_uri() . '/assets/css/magnific-popup.min.css');
    wp_enqueue_style('owl-carousel', get_template_directory_uri() . '/assets/css/owl.carousel.min.css');
    wp_enqueue_style('template-style', get_template_directory_uri() . '/assets/css/style.css'); // Original template styles
    wp_enqueue_style('main-style', get_stylesheet_uri()); // WordPress theme overrides (load last)

    // Scripts
    wp_enqueue_script('jquery'); // WordPress's built-in jQuery
    wp_enqueue_script('modernizr', get_template_directory_uri() . '/assets/js/modernizr.min.js', [], null, true);
    wp_enqueue_script('bootstrap', get_template_directory_uri() . '/assets/js/bootstrap.bundle.min.js', ['jquery'], null, true);
    wp_enqueue_script('imagesloaded', get_template_directory_uri() . '/assets/js/imagesloaded.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('magnific-popup', get_template_directory_uri() . '/assets/js/jquery.magnific-popup.min.js', ['jquery'], null, true);
    wp_enqueue_script('isotope', get_template_directory_uri() . '/assets/js/isotope.pkgd.min.js', ['jquery'], null, true);
    wp_enqueue_script('appear', get_template_directory_uri() . '/assets/js/jquery.appear.min.js', ['jquery'], null, true);
    wp_enqueue_script('easing', get_template_directory_uri() . '/assets/js/jquery.easing.min.js', ['jquery'], null, true);
    wp_enqueue_script('owl-carousel', get_template_directory_uri() . '/assets/js/owl.carousel.min.js', ['jquery'], null, true);
    wp_enqueue_script('counter-up', get_template_directory_uri() . '/assets/js/counter-up.js', ['jquery'], null, true);
    wp_enqueue_script('wow', get_template_directory_uri() . '/assets/js/wow.min.js', ['jquery'], null, true);
    wp_enqueue_script('countdown', get_template_directory_uri() . '/assets/js/countdown.min.js', ['jquery'], null, true);
    wp_enqueue_script('main-js', get_template_directory_uri() . '/assets/js/main.js', ['jquery', 'owl-carousel'], null, true);

    // Enqueue contact form script on contact page
    if (is_page('contact-us') || is_page_template('page-contact-us.php')) {
        wp_enqueue_script('contact-form-js', get_template_directory_uri() . '/assets/js/contact-form.js', ['jquery'], '1.0', true);

        // Localize script for AJAX
        wp_localize_script('contact-form-js', 'contact_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('contact_form_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'mytheme_enqueue_scripts');

add_filter('body_class', 'add_home2_body_class');
function add_home2_body_class($classes)
{
    if (is_front_page()) {
        $classes[] = 'home-2';
    }
    return $classes;
}

// Register widget areas
function mytheme_widgets_init()
{
    // Footer Widget Area 1
    register_sidebar(array(
        'name'          => __('Footer Widget 1', 'mytheme'),
        'id'            => 'footer-widget-1',
        'description'   => __('First footer widget area (Newsletter/About)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box about-us">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Widget Area 2
    register_sidebar(array(
        'name'          => __('Footer Widget 2', 'mytheme'),
        'id'            => 'footer-widget-2',
        'description'   => __('Second footer widget area (Quick Links)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Widget Area 3
    register_sidebar(array(
        'name'          => __('Footer Widget 3', 'mytheme'),
        'id'            => 'footer-widget-3',
        'description'   => __('Third footer widget area (Social Media)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Widget Area 4
    register_sidebar(array(
        'name'          => __('Footer Widget 4', 'mytheme'),
        'id'            => 'footer-widget-4',
        'description'   => __('Fourth footer widget area (Contact Info)', 'mytheme'),
        'before_widget' => '<div class="footer-widget-box list">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));

    // Footer Bottom Bar Widget Area
    register_sidebar(array(
        'name'          => __('Footer Bottom Bar', 'mytheme'),
        'id'            => 'footer-bottom-bar',
        'description'   => __('Footer bottom bar area (Copyright & Links)', 'mytheme'),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '',
        'after_title'   => '',
    ));

    // Header Top Bar Widget Area
    register_sidebar(array(
        'name'          => __('Header Top Bar', 'mytheme'),
        'id'            => 'header-top-bar',
        'description'   => __('Header top bar area (Contact & Social)', 'mytheme'),
        'before_widget' => '',
        'after_widget'  => '',
        'before_title'  => '',
        'after_title'   => '',
    ));
}
add_action('widgets_init', 'mytheme_widgets_init');

// Include nav walker
require_once get_template_directory() . '/class-bootstrap-navwalker.php';

// Include custom widgets
require_once get_template_directory() . '/inc/custom-widgets.php';

// Include AJAX handlers
require_once get_template_directory() . '/template/register/add-participant-handler.php';

// Auto-setup footer widgets with default content (runs once)
function mytheme_setup_default_widgets()
{
    if (get_option('mytheme_widgets_setup') !== 'done') {

        // Setup Newsletter Widget
        $newsletter_widget = array(
            'description' => 'We are many variations of passages available majority have suffered in some injected content of a page when looking at its layout humour words believable.'
        );

        // Setup Quick Links Widget
        $links_widget = array(
            'title' => 'Quick Links',
            'link_text_1' => 'About Us',
            'link_url_1' => '#',
            'link_text_2' => 'Update News',
            'link_url_2' => '#',
            'link_text_3' => 'Contact Us',
            'link_url_3' => '#',
            'link_text_4' => 'Testimonials',
            'link_url_4' => '#',
            'link_text_5' => 'Terms Of Service',
            'link_url_5' => '#',
            'link_text_6' => 'Privacy Policy',
            'link_url_6' => '#'
        );

        // Setup Social Widget
        $social_widget = array(
            'title' => 'Our Social',
            'facebook' => 'https://facebook.com',
            'twitter' => 'https://twitter.com',
            'instagram' => 'https://instagram.com',
            'youtube' => 'https://youtube.com',
            'whatsapp' => 'https://whatsapp.com',
            'linkedin' => 'https://linkedin.com'
        );

        // Setup Contact Widget
        $contact_widget = array(
            'title' => 'Get In Touch',
            'phone' => '****** 654 7898',
            'address' => '25/B Milford Road, New York',
            'email' => '<EMAIL>',
            'button_text' => 'Buy Ticket',
            'button_url' => '#'
        );

        // Mark as setup complete
        update_option('mytheme_widgets_setup', 'done');
    }
}

add_action('init', function () {
    if (!session_id()) {
        session_start();
    }
});

add_action('after_switch_theme', 'mytheme_setup_default_widgets');
add_filter('show_admin_bar', '__return_false');

// Contact Form Handler
function handle_contact_form_submission()
{
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['contact_nonce'], 'contact_form_nonce')) {
        wp_die('Security check failed');
    }

    // Sanitize and validate input data
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $subject = sanitize_text_field($_POST['subject']);
    $message = sanitize_textarea_field($_POST['message']);

    // Validate required fields
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        wp_send_json_error('All fields are required.');
        return;
    }

    // Validate email
    if (!is_email($email)) {
        wp_send_json_error('Please enter a valid email address.');
        return;
    }

    // Basic spam protection - check for suspicious patterns
    if (is_spam_submission($name, $email, $message)) {
        wp_send_json_error('Your submission appears to be spam. Please try again.');
        return;
    }

    // Rate limiting - prevent too many submissions from same IP
    if (is_rate_limited($_SERVER['REMOTE_ADDR'])) {
        wp_send_json_error('Too many submissions. Please wait before submitting again.');
        return;
    }

    // Save to database
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    $result = $wpdb->insert(
        $table_name,
        array(
            'name' => $name,
            'email' => $email,
            'subject' => $subject,
            'message' => $message,
            'submission_date' => current_time('mysql'),
            'ip_address' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ),
        array('%s', '%s', '%s', '%s', '%s', '%s', '%s')
    );

    if ($result === false) {
        error_log('Contact form database insert failed: ' . $wpdb->last_error);
        wp_send_json_error('There was an error saving your message. Please try again.');
        return;
    }

    // Send email notification
    $admin_email = get_option('admin_email');
    $site_name = get_bloginfo('name');

    $email_subject = sprintf('[%s] New Contact Form Submission: %s', $site_name, $subject);

    $email_message = sprintf(
        "New contact form submission received:\n\n" .
            "Name: %s\n" .
            "Email: %s\n" .
            "Subject: %s\n" .
            "Message:\n%s\n\n" .
            "Submitted on: %s\n" .
            "IP Address: %s",
        $name,
        $email,
        $subject,
        $message,
        current_time('mysql'),
        $_SERVER['REMOTE_ADDR']
    );

    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $site_name . ' <' . $admin_email . '>',
        'Reply-To: ' . $name . ' <' . $email . '>'
    );

    $mail_sent = wp_mail($admin_email, $email_subject, $email_message, $headers);

    if (!$mail_sent) {
        error_log('Contact form email failed to send');
    }

    // Send auto-reply email to user
    send_contact_form_auto_reply($name, $email, $subject);

    // Send success response
    wp_send_json_success('Thank you for your message! We will get back to you soon.');
}

// Send auto-reply email to user
function send_contact_form_auto_reply($name, $email, $subject)
{
    $site_name = get_bloginfo('name');
    $admin_email = get_option('admin_email');

    $reply_subject = sprintf('Thank you for contacting %s', $site_name);

    $reply_message = sprintf(
        "Dear %s,\n\n" .
            "Thank you for reaching out to us regarding: %s\n\n" .
            "We have received your message and will get back to you as soon as possible. " .
            "Our team typically responds within 24-48 hours during business days.\n\n" .
            "If your inquiry is urgent, please feel free to call us at (877) 964-6222.\n\n" .
            "Best regards,\n" .
            "The %s Team\n\n" .
            "---\n" .
            "This is an automated response. Please do not reply to this email.",
        $name,
        $subject,
        $site_name
    );

    $headers = array(
        'Content-Type: text/plain; charset=UTF-8',
        'From: ' . $site_name . ' <' . $admin_email . '>'
    );

    $auto_reply_sent = wp_mail($email, $reply_subject, $reply_message, $headers);

    if (!$auto_reply_sent) {
        error_log('Contact form auto-reply email failed to send to: ' . $email);
    }

    return $auto_reply_sent;
}

// Basic spam detection
function is_spam_submission($name, $email, $message)
{
    // Check for common spam patterns
    $spam_patterns = array(
        '/\b(viagra|cialis|casino|poker|loan|mortgage|insurance|pharmacy)\b/i',
        '/\b(click here|visit now|buy now|order now)\b/i',
        '/http[s]?:\/\/[^\s]{3,}/i', // Multiple URLs
        '/\b[A-Z]{10,}\b/', // Too many consecutive capitals
    );

    $content = $name . ' ' . $email . ' ' . $message;

    foreach ($spam_patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            return true;
        }
    }

    // Check for too many links
    if (substr_count($message, 'http') > 2) {
        return true;
    }

    // Check message length (too short or too long)
    if (strlen($message) < 10 || strlen($message) > 5000) {
        return true;
    }

    return false;
}

// Rate limiting function
function is_rate_limited($ip_address)
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Check submissions from this IP in the last hour
    $recent_submissions = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name
         WHERE ip_address = %s
         AND submission_date > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
        $ip_address
    ));

    // Allow maximum 3 submissions per hour per IP
    return $recent_submissions >= 3;
}

// Hook for logged-in users
add_action('wp_ajax_submit_contact_form', 'handle_contact_form_submission');
// Hook for non-logged-in users
add_action('wp_ajax_nopriv_submit_contact_form', 'handle_contact_form_submission');

// Create contact submissions table
function create_contact_submissions_table()
{
    global $wpdb;

    $table_name = $wpdb->prefix . 'contact_submissions';

    $charset_collate = $wpdb->get_charset_collate();

    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        name tinytext NOT NULL,
        email varchar(100) NOT NULL,
        subject tinytext NOT NULL,
        message text NOT NULL,
        submission_date datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text,
        status varchar(20) DEFAULT 'unread' NOT NULL,
        admin_notes text,
        PRIMARY KEY (id),
        KEY submission_date (submission_date),
        KEY status (status)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Update version option to track database changes
    update_option('contact_submissions_db_version', '1.0');
}

// Hook to create table on theme activation
add_action('after_switch_theme', 'create_contact_submissions_table');

// Add admin menu for contact submissions
function add_contact_submissions_admin_menu()
{
    add_menu_page(
        'Contact Submissions',
        'Contact Forms',
        'manage_options',
        'contact-submissions',
        'display_contact_submissions_page',
        'dashicons-email-alt',
        30
    );
}
add_action('admin_menu', 'add_contact_submissions_admin_menu');

// Change the "From" email address
add_filter('wp_mail_from', function ($original_email_address) {
    return '<EMAIL>';
});

// Change the "From" name
add_filter('wp_mail_from_name', function ($original_email_from) {
    return 'GoGlobalConference';
});

// AJAX handler to save input
add_action('wp_ajax_save_input_value', 'save_input_value_to_db');
add_action('wp_ajax_nopriv_save_input_value', 'save_input_value_to_db');
function save_input_value_to_db() {
    check_ajax_referer('save_input_nonce');

    $ggc_user_id    = sanitize_text_field($_POST['ggc_user_id'] ?? '');
    $ggc_value      = sanitize_text_field($_POST['ggc_value'] ?? '');
    $ggc_key        = sanitize_text_field($_POST['meta_key'] ?? '');
    $ggc_type       = sanitize_text_field($_POST['ggc_type'] ?? '');
    $ggc_checked  = sanitize_text_field($_POST['ggc_checked'] ?? '');

    // Debug logging
    error_log("AJAX Save Input - User ID: $ggc_user_id, Key: $ggc_key, Value: $ggc_value");

    if (!$ggc_user_id || $ggc_key === '') {
        wp_send_json_error('Missing required data (user_id or meta_key)');
        return;
    }

    if ($ggc_type == 'text') {
        // Allow empty values to be saved
        $result = save_text_input_to_db($ggc_user_id, $ggc_key, $ggc_value);
    } elseif ($ggc_type == 'radio') {
        $result = save_radio_input_to_db($ggc_user_id, $ggc_key, $ggc_value);
    } elseif ($ggc_type == 'checkbox') {
        $result = save_checkbox_input_to_db($ggc_user_id, $ggc_key, $ggc_value, $ggc_checked);
    }
    
    if ($result !== false) {
        wp_send_json_success('Saved!');
    } else {
        global $wpdb;
        $error_msg = $wpdb->last_error ? $wpdb->last_error : 'Failed to save - unknown error';
        wp_send_json_error($error_msg);
    }
}

// Custom function to update or insert into wp_custom_inputs
function save_text_input_to_db($ggc_user_id, $ggc_key, $ggc_value) {
    global $wpdb;
    $table = 'user_registration'; // No prefix based on other code usage

    // Check if row with this client_id exists
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE client_id = %s",
        $ggc_user_id
    ));

    if ($exists) {
        // Update the dynamic column with the value
        $result = $wpdb->update(
            $table,
            [$ggc_key => $ggc_value],          // column => value
            ['client_id' => $ggc_user_id],     // WHERE clause
            ['%s'],                             // format for value
            ['%s']                              // format for client_id
        );

        // Log any database errors for debugging
        if ($result === false && $wpdb->last_error) {
            error_log("Database update error: " . $wpdb->last_error);
            error_log("Attempted to update column: $ggc_key with value: $ggc_value for client_id: $ggc_user_id");
        }

        return $result;
    } else {
        error_log("No user found with client_id: $ggc_user_id");
        return false;
    }
}

// Custom function to update or insert into wp_custom_inputs
function save_radio_input_to_db($ggc_user_id, $ggc_key, $ggc_value) {
    global $wpdb;
    $table = 'user_registration'; // No prefix based on other code usage

    // Check if row with this client_id exists
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE client_id = %s",
        $ggc_user_id
    ));

    if ($exists) {

        // Update the dynamic column with the value
        if ($ggc_key == 'participated_before' || $ggc_key == 'consent_store_data') {
           if ($ggc_value == 'Yes') {
                $ggc_value = 1;
           } else {
                $ggc_value = 0;
           }
        }

        $result = $wpdb->update(
            $table,
            [$ggc_key => $ggc_value],          // column => value
            ['client_id' => $ggc_user_id],     // WHERE clause
            ['%s'],                             // format for value
            ['%s']                              // format for client_id
        );

        // Log any database errors for debugging
        if ($result === false && $wpdb->last_error) {
            error_log("Database update error: " . $wpdb->last_error);
            error_log("Attempted to update column: $ggc_key with value: $ggc_value for client_id: $ggc_user_id");
        }

        return $result;
    } else {
        error_log("No user found with client_id: $ggc_user_id");
        return false;
    }
}

// Custom function to update or insert into wp_custom_inputs
function save_checkbox_input_to_db($ggc_user_id, $ggc_key, $ggc_value, $ggc_checked) {
    global $wpdb;
    $table = 'user_registration'; // No prefix based on other code usage

    // Check if row with this client_id exists
    $exists = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table WHERE client_id = %s",
        $ggc_user_id
    ));

    if ($exists) {

        // Update the dynamic column with the value
        if ($ggc_key == 'attending_day1' || $ggc_key == 'attending_day2' || $ggc_key == 'attending_day3' || $ggc_key == 'attending_day4_visits' || $ggc_key == 'attending_day4_closing' || $ggc_key == 'confirm_participation' || $ggc_key == 'share_with_partners' || $ggc_key == 'post_event_contact' || $ggc_key == 'agree_terms' || $ggc_key == 'disagree_terms') {
            if ($ggc_checked == 'true') {
                $ggc_value = 1;
            } else {
                $ggc_value = 0;
            }
        }

        if ($ggc_key == 'primary_objectives' || $ggc_key == 'importing_regions' || $ggc_key == 'exporting_regions' || $ggc_key == 'industry_sectors' || $ggc_key == 'company_type' || $ggc_key == 'expansion_markets' || $ggc_key == 'preferred_meeting_time' || $ggc_key == 'memberships' || $ggc_key == 'referral_sources') {
            $results = $wpdb->get_var($wpdb->prepare(
                "SELECT $ggc_key FROM $table WHERE client_id = %s",
                $ggc_user_id
            ));
            if ($results) {
                $results_array = explode(",", $results);
                
                if ($ggc_checked == 'true') {
                    array_push($results_array, $ggc_value); 
                } else {
                    $key = array_search(strtolower($ggc_value), array_map('strtolower', $results_array));

                    if ($key !== false) {
                        unset($results_array[$key]);
                        $results_array = array_values($results_array);
                    }
                }
            } else {
                $results_array = array();
                array_push($results_array, $ggc_value); 
            }
            $ggc_value = implode(",", $results_array);
        }

        $result = $wpdb->update(
            $table,
            [$ggc_key => $ggc_value],          // column => value
            ['client_id' => $ggc_user_id],     // WHERE clause
            ['%s'],                             // format for value
            ['%s']                              // format for client_id
        );

        // Log any database errors for debugging
        if ($result === false && $wpdb->last_error) {
            error_log("Database update error: " . $wpdb->last_error);
            error_log("Attempted to update column: $ggc_key with value: $ggc_value for client_id: $ggc_user_id");
        }

        return $result;
    } else {
        error_log("No user found with client_id: $ggc_user_id");
        return false;
    }
}

// Display contact submissions admin page
function display_contact_submissions_page()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'contact_submissions';

    // Handle status updates
    if (isset($_POST['update_status']) && isset($_POST['submission_id']) && isset($_POST['new_status'])) {
        $submission_id = intval($_POST['submission_id']);
        $new_status = sanitize_text_field($_POST['new_status']);
        $admin_notes = sanitize_textarea_field($_POST['admin_notes']);

        $wpdb->update(
            $table_name,
            array(
                'status' => $new_status,
                'admin_notes' => $admin_notes
            ),
            array('id' => $submission_id),
            array('%s', '%s'),
            array('%d')
        );

        echo '<div class="notice notice-success"><p>Submission updated successfully!</p></div>';
    }

    // Handle bulk delete
    if (isset($_POST['bulk_action']) && $_POST['bulk_action'] === 'delete' && isset($_POST['submission_ids'])) {
        $submission_ids = array_map('intval', $_POST['submission_ids']);
        $placeholders = implode(',', array_fill(0, count($submission_ids), '%d'));
        $wpdb->query($wpdb->prepare("DELETE FROM $table_name WHERE id IN ($placeholders)", $submission_ids));

        echo '<div class="notice notice-success"><p>Selected submissions deleted successfully!</p></div>';
    }

    // Get submissions with pagination
    $per_page = 20;
    $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $offset = ($current_page - 1) * $per_page;

    $total_submissions = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    $submissions = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name ORDER BY submission_date DESC LIMIT %d OFFSET %d",
        $per_page,
        $offset
    ));

    $total_pages = ceil($total_submissions / $per_page);

?>
    <div class="wrap">
        <h1>Contact Form Submissions</h1>

        <div class="tablenav top">
            <div class="alignleft actions">
                <form method="post" style="display: inline;">
                    <select name="bulk_action">
                        <option value="">Bulk Actions</option>
                        <option value="delete">Delete</option>
                    </select>
                    <input type="submit" class="button" value="Apply">
                </form>
            </div>

            <div class="tablenav-pages">
                <span class="displaying-num"><?php echo $total_submissions; ?> items</span>
                <?php if ($total_pages > 1): ?>
                    <span class="pagination-links">
                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                            <a class="<?php echo $i === $current_page ? 'current' : ''; ?>"
                                href="?page=contact-submissions&paged=<?php echo $i; ?>"><?php echo $i; ?></a>
                        <?php endfor; ?>
                    </span>
                <?php endif; ?>
            </div>
        </div>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all">
                    </td>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Subject</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($submissions)): ?>
                    <tr>
                        <td colspan="7">No contact submissions found.</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($submissions as $submission): ?>
                        <tr>
                            <th class="check-column">
                                <input type="checkbox" name="submission_ids[]" value="<?php echo $submission->id; ?>">
                            </th>
                            <td><?php echo esc_html($submission->name); ?></td>
                            <td><a href="mailto:<?php echo esc_attr($submission->email); ?>"><?php echo esc_html($submission->email); ?></a></td>
                            <td><?php echo esc_html($submission->subject); ?></td>
                            <td><?php echo date('M j, Y g:i A', strtotime($submission->submission_date)); ?></td>
                            <td>
                                <span class="status-<?php echo esc_attr($submission->status); ?>">
                                    <?php echo ucfirst(esc_html($submission->status)); ?>
                                </span>
                            </td>
                            <td>
                                <a href="#" class="view-submission" data-id="<?php echo $submission->id; ?>">View</a> |
                                <a href="#" class="edit-submission" data-id="<?php echo $submission->id; ?>">Edit</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <style>
        .status-unread {
            color: #d63638;
            font-weight: bold;
        }

        .status-read {
            color: #00a32a;
        }

        .status-replied {
            color: #0073aa;
        }

        .submission-details {
            background: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #0073aa;
        }
    </style>

    <script>
        jQuery(document).ready(function($) {
            $('#cb-select-all').on('change', function() {
                $('input[name="submission_ids[]"]').prop('checked', this.checked);
            });

            $('.view-submission').on('click', function(e) {
                e.preventDefault();
                var submissionId = $(this).data('id');
                // Add modal or expand functionality here
                alert('View submission #' + submissionId + ' - Modal functionality can be added here');
            });

            $('.edit-submission').on('click', function(e) {
                e.preventDefault();
                var submissionId = $(this).data('id');
                // Add edit functionality here
                alert('Edit submission #' + submissionId + ' - Edit functionality can be added here');
            });
        });
    </script>
<?php
}
