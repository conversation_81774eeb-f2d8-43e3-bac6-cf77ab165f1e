<?php
error_reporting(E_ALL);  // Report all errors, warnings, and notices
ini_set('display_errors', 1);  // Display errors on the screen
ini_set('display_startup_errors', 1);  // Display startup errors

global $wpdb;
$table_name = 'user_registration';
$payments_table = 'user_payments';

$query = "
    SELECT *
    FROM $payments_table
";

$results = $wpdb->get_results($query);

$matching_client_ids = 0;
$non_matching_client_ids = 0;
$empty_client_ids_array = array();
$non_matching_client_ids_array = array();

foreach ($results as $row) {
    if (!empty($row->client_id)) {
        $query2 = "
            SELECT *
            FROM $table_name
            WHERE client_id = '$row->client_id'
            ";
        $results2 = $wpdb->get_results($query2);
        if (!empty($results2) ) {
            $matching_client_ids++;
        } else {
            $non_matching_client_ids++;
            $non_matching_client_ids_array[] = $row->pay_email;
        }
    } else {
        $empty_client_ids_array[] = $row->pay_email . ' - ' . $row->amount . ' - ' . $row->product_name;
    }
}

echo '<h2>Payments Table Empty Client Id</h2>';
foreach ($empty_client_ids_array as $email) {
    echo $email . '<br>';
}

echo '<hr>';

echo '<h2>Payments Table Non-Matching Client Id</h2>';
foreach ($non_matching_client_ids_array as $email) {
    echo $email . '<br>';
}

echo '<hr>';

echo '<h2>Payments Table Matching Client Id</h2>';
echo 'Total Results: ' . count($results);
echo '<br>';
echo 'Total Matching Client Id: ' . $matching_client_ids;

echo '<hr>';

$query = "
    SELECT *
    FROM $table_name
";

$results = $wpdb->get_results($query);

$empty_client_ids = array();
foreach ($results as $row) {
    if (empty($row->client_id)) {
        $empty_client_ids[] = $row->email;
    }
}

echo '<h2>Registration Table Empty Client Id</h2>';
foreach ($empty_client_ids as $email) {
    echo $email . '<br>';
}
?>