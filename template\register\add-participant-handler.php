<?php
// AJAX handler for adding new participants
add_action('wp_ajax_add_new_participant', 'handle_add_new_participant');
add_action('wp_ajax_nopriv_add_new_participant', 'handle_add_new_participant');

function handle_add_new_participant()
{
    // Verify nonce for security
    if (!wp_verify_nonce($_POST['nonce'], 'add_participant_nonce')) {
        wp_die('Security check failed');
    }

    // Sanitize input data
    $firstName = sanitize_text_field($_POST['firstName']);
    $lastName = sanitize_text_field($_POST['lastName']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);

    // Validate required fields
    if (empty($firstName) || empty($lastName) || empty($email)) {
        wp_send_json_error(array('message' => 'Please fill in all required fields.'));
        return;
    }

    // Validate email format
    if (!is_email($email)) {
        wp_send_json_error(array('message' => 'Please enter a valid email address.'));
        return;
    }

    global $wpdb;
    $registration_table = 'user_registration';

    try {
        // Generate unique client_id using the same format as pricing.php
        $unique_id = date('YmdHis') . '_' . bin2hex(random_bytes(4));

        // Generate a random password
        $digits = '';
        for ($i = 0; $i < 5; $i++) {
            $digits .= mt_rand(1, 9);
        }
        $password = $digits;


        // Prepare minimal data for user_registration table - only essential fields
        $registration_data = array(
            'client_id' => $unique_id,
            'password' => $password,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'contact_phone' => $phone,
            'email' => $email
        );

        $registration_format = array(
            '%s', // client_id
            '%s', // password
            '%s', // first_name
            '%s', // last_name
            '%s', // contact_phone
            '%s'  // rep_email
        );

        // Insert into user_registration table
        $registration_result = $wpdb->insert($registration_table, $registration_data, $registration_format);

        if ($registration_result === false) {
            wp_send_json_error(array('message' => 'Failed to create user registration. Please try again.'));
            return;
        }

        set_query_var('email', $email);
        set_query_var('firstName', $firstName);
        set_query_var('lastName', $lastName);
        set_query_var('phone', $phone);
        get_template_part('template/register/submit_mautic_added_user');

        wp_send_json_success(array(
            'message' => 'Participant added successfully!',
            'client_id' => $unique_id,
            'email' => $email
        ));
    } catch (Exception $e) {
        wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
    }
}
