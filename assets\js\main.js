/*=====================================================
Template Name   : Eventu
Description     : Event And Conference HTML5 Template
Author          : LunarTemp
Version         : 1.0
=======================================================*/


(function ($) {
    
    "use strict";

    // multi level dropdown menu
    $('.dropdown-menu a.dropdown-toggle').on('click', function (e) {
        if (!$(this).next().hasClass('show')) {
            $(this).parents('.dropdown-menu').first().find('.show').removeClass('show');
        }
        var $subMenu = $(this).next('.dropdown-menu');
        $subMenu.toggleClass('show');

        $(this).parents('li.nav-item.dropdown.show').on('hidden.bs.dropdown', function (e) {
            $('.dropdown-submenu .show').removeClass('show');
        });
        return false;
    });


    //Header Search
    if ($('.search-box-outer').length) {
        $('.search-box-outer').on('click', function () {
            $('body').addClass('search-active');
        });
        $('.close-search').on('click', function () {
            $('body').removeClass('search-active');
        });
    }


    // data-background    
    $(document).on('ready', function () {
        $("[data-background]").each(function () {
            $(this).css("background-image", "url(" + $(this).attr("data-background") + ")");
        });
    });


    // sidebar popup 
    $('.sidebar-btn').on('click', function() {
        $('.sidebar-popup').addClass('open');
        $('.sidebar-wrapper').addClass('open');
    });
    $('.close-sidebar-popup, .sidebar-popup').on('click', function() {
        $('.sidebar-popup').removeClass('open');
        $('.sidebar-wrapper').removeClass('open');
    });


    // wow init
    new WOW().init();


    // hero slider
    $('.hero-slider').owlCarousel({
        loop: true,
        nav: true,
        dots: true,
        margin: 0,
        autoplay: true,
        autoplayHoverPause: true,
        autoplayTimeout: 5000,
        items: 1,
        navText: [
            "<i class='far fa-long-arrow-left'></i>",
            "<i class='far fa-long-arrow-right'></i>"
        ],

        onInitialized: function(event) {
        var $firstAnimatingElements = $('.owl-item').eq(event.item.index).find("[data-animation]");
        doAnimations($firstAnimatingElements);
        },

        onChanged: function(event){
        var $firstAnimatingElements = $('.owl-item').eq(event.item.index).find("[data-animation]");
        doAnimations($firstAnimatingElements);
        }
        });

    //hero slider do animations
    function doAnimations(elements) {
		var animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';
		elements.each(function () {
			var $this = $(this);
			var $animationDelay = $this.data('delay');
			var $animationDuration = $this.data('duration');
			var $animationType = 'animated ' + $this.data('animation');
			$this.css({
				'animation-delay': $animationDelay,
				'-webkit-animation-delay': $animationDelay,
                'animation-duration': $animationDuration,
                '-webkit-animation-duration': $animationDuration,
			});
			$this.addClass($animationType).one(animationEndEvents, function () {
				$this.removeClass($animationType);
			});
		});
	}


    // testimonial-slider
    $('.testimonial-slider').owlCarousel({
        loop: true,
        margin: 30,
        nav: false,
        dots: true,
        autoplay: true,
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 2
            },
            1000: {
                items: 3
            }
        }
    });

	// video-testimonial-slider
    $('.video-testimonial-slider').owlCarousel({
        loop: true,
        margin: 0,
        nav: false,
        dots: true,
        autoplay: false,
        autoplayTimeout: 6000,
        autoplayHoverPause: true,
        items: 1,
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 1
            },
            1000: {
                items: 1
            }
        }
    });

    // Video testimonial modal functionality
    $('.video-play-btn').on('click', function() {
        var videoUrl = $(this).data('video-url');
        var videoType = $(this).data('video-type');
        var modal = $(this).closest('.testimonial-single').find('.video-modal');

        if (videoType === 'local') {
            // Handle local video files
            var video = modal.find('video');
            video.attr('src', videoUrl);
            video[0].load(); // Reload the video element
        } else {
            // Handle YouTube/external videos (fallback)
            var iframe = modal.find('iframe');
            iframe.attr('src', videoUrl + '?autoplay=1');
        }

        modal.show();

        // Prevent body scroll when modal is open
        $('body').css('overflow', 'hidden');
    });

    // Close video modal
    $('.video-close').on('click', function() {
        var modal = $(this).closest('.video-modal');
        var video = modal.find('video');
        var iframe = modal.find('iframe');

        // Stop and reset video
        if (video.length) {
            video[0].pause();
            video[0].currentTime = 0;
        }

        // Stop iframe video
        if (iframe.length) {
            iframe.attr('src', '');
        }

        modal.hide();

        // Restore body scroll
        $('body').css('overflow', 'auto');
    });

    // Close modal when clicking outside
    $('.video-modal').on('click', function(e) {
        if (e.target === this) {
            var video = $(this).find('video');
            var iframe = $(this).find('iframe');

            // Stop and reset video
            if (video.length) {
                video[0].pause();
                video[0].currentTime = 0;
            }

            // Stop iframe video
            if (iframe.length) {
                iframe.attr('src', '');
            }

            $(this).hide();
            $('body').css('overflow', 'auto');
        }
    });

    // Close modal with ESC key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            $('.video-modal').each(function() {
                if ($(this).is(':visible')) {
                    var video = $(this).find('video');
                    var iframe = $(this).find('iframe');

                    // Stop and reset video
                    if (video.length) {
                        video[0].pause();
                        video[0].currentTime = 0;
                    }

                    // Stop iframe video
                    if (iframe.length) {
                        iframe.attr('src', '');
                    }

                    $(this).hide();
                    $('body').css('overflow', 'auto');
                }
            });
        }
    });
	
	 // Play button clicked - open modal, play video, and stop autoplay
    $('.video-play-btn').on('click', function () {
        var $btn = $(this);
        var $wrapper = $btn.closest('.testimonial-video-wrapper');
        var $modal = $wrapper.find('.video-modal');
        var $video = $modal.find('video').get(0);

        $modal.show();
        $video.play();

    });

    // Close button clicked - hide modal, pause video, and resume autoplay
    $('.video-close').on('click', function () {
        var $modal = $(this).closest('.video-modal');
        var $video = $modal.find('video').get(0);

        $video.pause();
        $modal.hide();

    });
	
    // partner-slider
    $('.partner-slider').owlCarousel({
        loop: true,
        margin: 70,
        nav: false,
        navText: [
            "<i class='icofont-long-arrow-left'></i>",
            "<i class='icofont-long-arrow-right'></i>"
        ],
        dots: false,
        autoplay: true,
        responsive: {
            0: {
                items: 2
            },
            600: {
                items: 3
            },
            1000: {
                items: 6
            }
        }
    });



    // preloader
    $(window).on('load', function () {
        $(".preloader").fadeOut("slow");
    });


    // fun fact counter
    $('.counter').countTo();
    $('.counter-box').appear(function () {
        $('.counter').countTo();
    }, {
        accY: -100
    });


    // magnific popup init
    $(".popup-gallery").magnificPopup({
        delegate: '.popup-img',
        type: 'image',
        gallery: {
            enabled: true
        },
    });

    $(".popup-youtube, .popup-vimeo, .popup-gmaps").magnificPopup({
        type: "iframe",
        mainClass: "mfp-fade",
        removalDelay: 160,
        preloader: false,
        fixedContentPos: false
    });



    // scroll to top
    $(window).scroll(function () {
        if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
            $("#scroll-top").addClass('active');
        } else {
            $("#scroll-top").removeClass('active');
        }
    });

    $("#scroll-top").on('click', function () {
        $("html, body").animate({ scrollTop: 0 }, 1500);
        return false;
    });


    // navbar fixed top
    $(window).scroll(function () {
        if ($(this).scrollTop() > 140) {
            $('.navbar').addClass("fixed-top");
        } else {
            $('.navbar').removeClass("fixed-top");
        }
    });


    // countdown
    $('[data-countdown]').each(function() {
        let finalDate = $(this).data('countdown');
        $(this).countdown(finalDate, function(event) {
            $(this).html(event.strftime(
                '<div class="time-wrap">' + '<span class="time"><span>%-D</span><span class="unit">Day%!D</span></span>' + ' <span class="divider">:</span> ' + '<span class="time"><span>%H</span><span class="unit">Hour%!H</span></span>' + ' <span class="divider">:</span> ' + '<span class="time"><span>%M</span><span class="unit">Min%!M</span></span>' + ' <span class="divider">:</span> ' + '<span class="time"><span>%S</span><span class="unit">Sec%!S</span></span>' + '</div>'
            ));
        });
    });


    // copywrite date
    let date = new Date().getFullYear();
    $("#date").html(date);


    // bootstrap tooltip enable
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })


})(jQuery);










