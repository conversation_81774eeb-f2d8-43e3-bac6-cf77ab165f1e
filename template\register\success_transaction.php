<div class="container">
    <div class="row">
        <div class="col-lg-12">
            <div class="success-transaction">
                <!-- Simple Check Icon in Circle -->
                <div id="my-check-box">
                    <span style="color: #fff; font-size: 250px;">✓</span>
                </div>

                <!-- Message Section -->
                <div>
                    <h1 style="font-size: 35px; margin: 0; color: #c1033f;">Thank You!</h1>
                    <p style="margin: 0;"><b style="color: #c1033f; font-size: 30px; font-weight: normal;">Your payment was successful.</b></p>
                    <ul class="thankyou-list">
                        <li>You will need to <b>complete a B2B matchmaking profile</b>
                            in order to complete the registration process.
                        </li>
                        <li>We have <b>sent you an email</b> with your <b>login credentials</b> to return to the site to complete or update your business profile</li>
                        <li>Please check your spam folder if you do not see the email.</li>
                        <li>Once you have completed your business profile, you may return to view and/or update your profile with your login credentials.</li>
                    </ul>
                </div>

            </div>
        </div>
        <div class="col-lg-12">
            <div class="site-heading text-center wow fadeInDown" data-wow-delay=".25s" style="visibility: visible; animation-delay: 0.25s; animation-name: fadeInDown;">
                <span class="site-title-tagline">You are not fully registered</span>
                <h2 class="site-title">until you complete <span>the B2B matchmaking profile</span></h2>
            </div>
        </div>
        <div class="col-lg-12">
            <div style="display: flex; justify-content: center; align-items: center;">
                <a href="/ggc-registration-form/" class="theme-btn big-btn">Proceed to the next step to complete your Business Profile. <i class="fas fa-arrow-right"></i></a>
            </div>
        </div>
        <div class="col-lg-12">
            <?php
            global $wpdb;

            // Table names (automatically add prefix if needed)
            $payments_table = 'user_payments';

            // Get the current user ID from session
            $user_id = $_SESSION['ggc_user_id'];

            if (!empty($user_id)) {
                // Safely prepare the query
                $query = $wpdb->prepare(
                    "SELECT * FROM $payments_table WHERE client_id = %s",
                    $user_id
                );

                $results = $wpdb->get_row($query);
                $_SESSION['ggc_user_id'] = $results->client_id;
                $_SESSION['ggc_email'] = strtolower($results->pay_email);
                $_SESSION['ggc_login'] = true;
            } else {
                echo "User ID not set.";
            }
            ?>
            <div class="table-thankyou" style="margin-bottom: 50px;">
                <table>
                    <tr>
                        <td>
                            Name: <b><?= $results->pay_name; ?></b>
                        </td>
                        <td>
                            Email: <b><?= $results->pay_email; ?></b>
                        </td>
                        <td>
                            Phone: <b><?= $results->pay_phone; ?></b>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Ticket: <b><?= $results->product_name; ?></b>
                        </td>
                        <td>
                            Quantity: <b><?= $results->quantity; ?></b>
                        </td>
                        <td>
                            Total: <b><?= $results->amount; ?> <?= $results->currency; ?></b>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>