<?php
/**
 * Custom Widgets for Footer
 */

// Newsletter Widget
class Mytheme_Newsletter_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'mytheme_newsletter',
            __('Footer Newsletter', 'mytheme'),
            array('description' => __('Newsletter subscription widget for footer', 'mytheme'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        // Logo
        if (has_custom_logo()) {
            $custom_logo_id = get_theme_mod('custom_logo');
            $logo = wp_get_attachment_image_src($custom_logo_id, 'full');
            echo '<a href="' . esc_url(home_url('/')) . '" class="footer-logo">';
            echo '<img src="' . esc_url($logo[0]) . '" alt="' . get_bloginfo('name') . '">';
            echo '</a>';
        } else {
            echo '<a href="' . esc_url(home_url('/')) . '" class="footer-logo">';
            echo '<h3 style="color: white;">' . get_bloginfo('name') . '</h3>';
            echo '</a>';
        }
        
        // Description
        $description = !empty($instance['description']) ? $instance['description'] : 'We are many variations of passages available majority have suffered in some injected content of a page when looking at its layout humour words believable.';
        echo '<p class="mb-3">' . esc_html($description) . '</p>';
        
        // Newsletter
        echo '<div class="footer-newsletter">';
        echo '<p>Subscribe Our Newsletter</p>';
        echo '<div class="subscribe-form">';
        echo '<form action="#" method="post">';
        echo '<div class="form-group">';
        echo '<input type="email" class="form-control" placeholder="Your Email" required>';
        echo '<button class="theme-btn" type="submit">';
        echo '<span class="far fa-paper-plane"></span> Subscribe';
        echo '</button>';
        echo '</div>';
        echo '</form>';
        echo '</div>';
        echo '</div>';
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $description = !empty($instance['description']) ? $instance['description'] : '';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('description'); ?>"><?php _e('Description:', 'mytheme'); ?></label>
            <textarea class="widefat" id="<?php echo $this->get_field_id('description'); ?>" name="<?php echo $this->get_field_name('description'); ?>" rows="4"><?php echo esc_attr($description); ?></textarea>
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['description'] = (!empty($new_instance['description'])) ? sanitize_textarea_field($new_instance['description']) : '';
        return $instance;
    }
}

// Quick Links Widget
class Mytheme_Quick_Links_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'mytheme_quick_links',
            __('Footer Quick Links', 'mytheme'),
            array('description' => __('Quick links widget for footer', 'mytheme'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        $title = !empty($instance['title']) ? $instance['title'] : 'Quick Links';
        echo $args['before_title'] . esc_html($title) . $args['after_title'];
        
        echo '<ul class="footer-list">';
        
        // Get links from instance
        for ($i = 1; $i <= 6; $i++) {
            $link_text = !empty($instance["link_text_$i"]) ? $instance["link_text_$i"] : '';
            $link_url = !empty($instance["link_url_$i"]) ? $instance["link_url_$i"] : '';
            
            if ($link_text && $link_url) {
                echo '<li><a href="' . esc_url($link_url) . '"><i class="fas fa-caret-right"></i> ' . esc_html($link_text) . '</a></li>';
            }
        }
        
        echo '</ul>';
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Quick Links';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
        
        for ($i = 1; $i <= 6; $i++) {
            $link_text = !empty($instance["link_text_$i"]) ? $instance["link_text_$i"] : '';
            $link_url = !empty($instance["link_url_$i"]) ? $instance["link_url_$i"] : '';
            ?>
            <p>
                <label for="<?php echo $this->get_field_id("link_text_$i"); ?>"><?php echo sprintf(__('Link %d Text:', 'mytheme'), $i); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id("link_text_$i"); ?>" name="<?php echo $this->get_field_name("link_text_$i"); ?>" type="text" value="<?php echo esc_attr($link_text); ?>">
            </p>
            <p>
                <label for="<?php echo $this->get_field_id("link_url_$i"); ?>"><?php echo sprintf(__('Link %d URL:', 'mytheme'), $i); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id("link_url_$i"); ?>" name="<?php echo $this->get_field_name("link_url_$i"); ?>" type="url" value="<?php echo esc_attr($link_url); ?>">
            </p>
            <?php
        }
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        
        for ($i = 1; $i <= 6; $i++) {
            $instance["link_text_$i"] = (!empty($new_instance["link_text_$i"])) ? sanitize_text_field($new_instance["link_text_$i"]) : '';
            $instance["link_url_$i"] = (!empty($new_instance["link_url_$i"])) ? esc_url_raw($new_instance["link_url_$i"]) : '';
        }
        
        return $instance;
    }
}

// Social Media Widget
class Mytheme_Social_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'mytheme_social',
            __('Footer Social Media', 'mytheme'),
            array('description' => __('Social media links widget for footer', 'mytheme'))
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        $title = !empty($instance['title']) ? $instance['title'] : 'Our Social';
        echo $args['before_title'] . esc_html($title) . $args['after_title'];

        echo '<ul class="footer-list social">';

        $social_networks = array(
            'facebook' => array('icon' => 'fab fa-facebook', 'name' => 'Facebook'),
            'twitter' => array('icon' => 'fab fa-x-twitter', 'name' => 'Twitter'),
            'instagram' => array('icon' => 'fab fa-instagram', 'name' => 'Instagram'),
            'youtube' => array('icon' => 'fab fa-youtube', 'name' => 'Youtube'),
            'whatsapp' => array('icon' => 'fab fa-whatsapp', 'name' => 'Whatsapp'),
            'linkedin' => array('icon' => 'fab fa-linkedin-in', 'name' => 'Linkedin')
        );

        foreach ($social_networks as $network => $data) {
            $url = !empty($instance[$network]) ? $instance[$network] : '';
            if ($url) {
                echo '<li><a href="' . esc_url($url) . '" target="_blank"><i class="' . esc_attr($data['icon']) . '"></i> ' . esc_html($data['name']) . '</a></li>';
            }
        }

        echo '</ul>';
        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Our Social';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php

        $social_networks = array(
            'facebook' => 'Facebook',
            'twitter' => 'Twitter',
            'instagram' => 'Instagram',
            'youtube' => 'Youtube',
            'whatsapp' => 'Whatsapp',
            'linkedin' => 'Linkedin'
        );

        foreach ($social_networks as $network => $name) {
            $url = !empty($instance[$network]) ? $instance[$network] : '';
            ?>
            <p>
                <label for="<?php echo $this->get_field_id($network); ?>"><?php echo sprintf(__('%s URL:', 'mytheme'), $name); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id($network); ?>" name="<?php echo $this->get_field_name($network); ?>" type="url" value="<?php echo esc_attr($url); ?>">
            </p>
            <?php
        }
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';

        $social_networks = array('facebook', 'twitter', 'instagram', 'youtube', 'whatsapp', 'linkedin');
        foreach ($social_networks as $network) {
            $instance[$network] = (!empty($new_instance[$network])) ? esc_url_raw($new_instance[$network]) : '';
        }

        return $instance;
    }
}

// Contact Widget
class Mytheme_Contact_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'mytheme_contact',
            __('Footer Contact', 'mytheme'),
            array('description' => __('Contact information widget for footer', 'mytheme'))
        );
    }

    public function widget($args, $instance) {
        echo $args['before_widget'];

        $title = !empty($instance['title']) ? $instance['title'] : 'Get In Touch';
        echo $args['before_title'] . esc_html($title) . $args['after_title'];

        echo '<ul class="footer-contact">';

        // Phone
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        if ($phone) {
            echo '<li><a href="tel:' . esc_attr($phone) . '"><i class="far fa-phone"></i>' . esc_html($phone) . '</a></li>';
        }

        // Address
        $address = !empty($instance['address']) ? $instance['address'] : '';
        if ($address) {
            echo '<li><i class="far fa-map-marker-alt"></i>' . esc_html($address) . '</li>';
        }

        // Email
        $email = !empty($instance['email']) ? $instance['email'] : '';
        if ($email) {
            echo '<li><a href="mailto:' . esc_attr($email) . '"><i class="far fa-envelope"></i>' . esc_html($email) . '</a></li>';
        }

        echo '</ul>';

        // Ticket button
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : 'Buy Ticket';
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '#';

        echo '<div class="footer-request">';
        echo '<p>Book Your Ticket</p>';
        echo '<a href="' . esc_url($button_url) . '" class="theme-btn">' . esc_html($button_text) . '<i class="fas fa-arrow-right"></i></a>';
        echo '</div>';

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Get In Touch';
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        $address = !empty($instance['address']) ? $instance['address'] : '';
        $email = !empty($instance['email']) ? $instance['email'] : '';
        $button_text = !empty($instance['button_text']) ? $instance['button_text'] : 'Buy Ticket';
        $button_url = !empty($instance['button_url']) ? $instance['button_url'] : '#';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('Title:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('phone'); ?>"><?php _e('Phone:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('phone'); ?>" name="<?php echo $this->get_field_name('phone'); ?>" type="text" value="<?php echo esc_attr($phone); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('address'); ?>"><?php _e('Address:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('address'); ?>" name="<?php echo $this->get_field_name('address'); ?>" type="text" value="<?php echo esc_attr($address); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('email'); ?>"><?php _e('Email:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('email'); ?>" name="<?php echo $this->get_field_name('email'); ?>" type="email" value="<?php echo esc_attr($email); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('button_text'); ?>"><?php _e('Button Text:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('button_text'); ?>" name="<?php echo $this->get_field_name('button_text'); ?>" type="text" value="<?php echo esc_attr($button_text); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('button_url'); ?>"><?php _e('Button URL:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('button_url'); ?>" name="<?php echo $this->get_field_name('button_url'); ?>" type="url" value="<?php echo esc_attr($button_url); ?>">
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['phone'] = (!empty($new_instance['phone'])) ? sanitize_text_field($new_instance['phone']) : '';
        $instance['address'] = (!empty($new_instance['address'])) ? sanitize_text_field($new_instance['address']) : '';
        $instance['email'] = (!empty($new_instance['email'])) ? sanitize_email($new_instance['email']) : '';
        $instance['button_text'] = (!empty($new_instance['button_text'])) ? sanitize_text_field($new_instance['button_text']) : '';
        $instance['button_url'] = (!empty($new_instance['button_url'])) ? esc_url_raw($new_instance['button_url']) : '';

        return $instance;
    }
}

// Footer Bottom Bar Widget
class Mytheme_Footer_Bottom_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'mytheme_footer_bottom',
            __('Footer Bottom Bar', 'mytheme'),
            array('description' => __('Copyright and footer menu widget', 'mytheme'))
        );
    }

    public function widget($args, $instance) {
        echo '<div class="container">';
        echo '<div class="copyright">';
        echo '<div class="row">';

        // Left side - Copyright
        echo '<div class="col-md-6 align-self-center">';
        echo '<p class="copyright-text">';

        $copyright_text = !empty($instance['copyright_text']) ? $instance['copyright_text'] : '&copy; Copyright <span id="date"></span> <a href="' . esc_url(home_url('/')) . '"> ' . get_bloginfo('name') . ' </a> All Rights Reserved.';
        echo $copyright_text;

        echo '</p>';
        echo '</div>';

        // Right side - Footer Menu
        echo '<div class="col-md-6 align-self-center">';
        echo '<ul class="footer-menu">';

        // Get menu links from instance
        for ($i = 1; $i <= 5; $i++) {
            $link_text = !empty($instance["menu_text_$i"]) ? $instance["menu_text_$i"] : '';
            $link_url = !empty($instance["menu_url_$i"]) ? $instance["menu_url_$i"] : '';

            if ($link_text && $link_url) {
                echo '<li><a href="' . esc_url($link_url) . '">' . esc_html($link_text) . '</a></li>';
            }
        }

        // Default links if none configured
        if (empty($instance['menu_text_1'])) {
            echo '<li><a href="#">Support</a></li>';
            echo '<li><a href="#">Terms Of Services</a></li>';
            echo '<li><a href="#">Privacy Policy</a></li>';
        }

        echo '</ul>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    public function form($instance) {
        $copyright_text = !empty($instance['copyright_text']) ? $instance['copyright_text'] : '';
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('copyright_text'); ?>"><?php _e('Copyright Text (use <span id="date"></span> for auto year):', 'mytheme'); ?></label>
            <textarea class="widefat" id="<?php echo $this->get_field_id('copyright_text'); ?>" name="<?php echo $this->get_field_name('copyright_text'); ?>" rows="3"><?php echo esc_attr($copyright_text); ?></textarea>
            <small>Leave empty for default. Use HTML allowed.</small>
        </p>

        <h4><?php _e('Footer Menu Links:', 'mytheme'); ?></h4>
        <?php

        for ($i = 1; $i <= 5; $i++) {
            $menu_text = !empty($instance["menu_text_$i"]) ? $instance["menu_text_$i"] : '';
            $menu_url = !empty($instance["menu_url_$i"]) ? $instance["menu_url_$i"] : '';
            ?>
            <p>
                <label for="<?php echo $this->get_field_id("menu_text_$i"); ?>"><?php echo sprintf(__('Menu Link %d Text:', 'mytheme'), $i); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id("menu_text_$i"); ?>" name="<?php echo $this->get_field_name("menu_text_$i"); ?>" type="text" value="<?php echo esc_attr($menu_text); ?>">
            </p>
            <p>
                <label for="<?php echo $this->get_field_id("menu_url_$i"); ?>"><?php echo sprintf(__('Menu Link %d URL:', 'mytheme'), $i); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id("menu_url_$i"); ?>" name="<?php echo $this->get_field_name("menu_url_$i"); ?>" type="url" value="<?php echo esc_attr($menu_url); ?>">
            </p>
            <?php
        }
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['copyright_text'] = (!empty($new_instance['copyright_text'])) ? wp_kses_post($new_instance['copyright_text']) : '';

        for ($i = 1; $i <= 5; $i++) {
            $instance["menu_text_$i"] = (!empty($new_instance["menu_text_$i"])) ? sanitize_text_field($new_instance["menu_text_$i"]) : '';
            $instance["menu_url_$i"] = (!empty($new_instance["menu_url_$i"])) ? esc_url_raw($new_instance["menu_url_$i"]) : '';
        }

        return $instance;
    }
}

// Header Top Bar Widget
class Mytheme_Header_Top_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'mytheme_header_top',
            __('Header Top Bar', 'mytheme'),
            array('description' => __('Header top bar with contact info and social links', 'mytheme'))
        );
    }

    public function widget($args, $instance) {
        echo '<div class="header-top">';
        echo '<div class="container">';
        echo '<div class="header-top-wrap">';
        echo '<div class="row align-items-center">';

        // Left side - Contact Info
        echo '<div class="col-md-6">';
        echo '<div class="header-top-left">';
        echo '<div class="header-top-contact">';
        echo '<ul>';

        // Location
        $location = !empty($instance['location']) ? $instance['location'] : '';
        if ($location) {
            echo '<li><a href="#"><i class="far fa-location-dot"></i>' . esc_html($location) . '</a></li>';
        }

        // Email
        $email = !empty($instance['email']) ? $instance['email'] : '';
        if ($email) {
            echo '<li><a href="mailto:' . esc_attr($email) . '"><i class="far fa-envelopes"></i>' . esc_html($email) . '</a></li>';
        }

        // Phone
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        if ($phone) {
            echo '<li><a href="tel:' . esc_attr($phone) . '"><i class="far fa-phone-volume"></i> ' . esc_html($phone) . '</a></li>';
        }

        // Default content if none configured
        if (empty($instance['location']) && empty($instance['email']) && empty($instance['phone'])) {
            echo '<li><a href="#"><i class="far fa-location-dot"></i>25/B Milford Road, New York</a></li>';
            echo '<li><a href="mailto:<EMAIL>"><i class="far fa-envelopes"></i><EMAIL></a></li>';
            echo '<li><a href="tel:+21236547898"><i class="far fa-phone-volume"></i> ****** 654 7898</a></li>';
        }

        echo '</ul>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        // Right side - Language & Social
        echo '<div class="col-md-6">';
        echo '<div class="header-top-right">';

        // Language Dropdown
        $show_language = !empty($instance['show_language']) ? $instance['show_language'] : '';
        if ($show_language) {
            echo '<div class="header-top-lang">';
            echo '<div class="dropdown">';
            echo '<a href="#" class="top-lang dropdown-toggle" data-bs-toggle="dropdown"><i class="fal fa-globe"></i> Language</a>';
            echo '<ul class="dropdown-menu dropdown-menu-end">';
            echo '<li><a class="dropdown-item" href="#">English</a></li>';
            echo '<li><a class="dropdown-item" href="#">German</a></li>';
            echo '<li><a class="dropdown-item" href="#">Russian</a></li>';
            echo '<li><a class="dropdown-item" href="#">Spanish</a></li>';
            echo '</ul>';
            echo '</div>';
            echo '</div>';
        }

        // Social Links
        echo '<div class="header-top-social">';
        echo '<span>Follow Us:</span>';

        $social_networks = array(
            'facebook' => 'fab fa-facebook',
            'twitter' => 'fab fa-x-twitter',
            'instagram' => 'fab fa-instagram',
            'linkedin' => 'fab fa-linkedin'
        );

        $has_social = false;
        foreach ($social_networks as $network => $icon) {
            $url = !empty($instance[$network]) ? $instance[$network] : '';
            if ($url) {
                echo '<a href="' . esc_url($url) . '" target="_blank"><i class="' . esc_attr($icon) . '"></i></a>';
                $has_social = true;
            }
        }

        // Default social if none configured
        if (!$has_social) {
            echo '<a href="#"><i class="fab fa-facebook"></i></a>';
            echo '<a href="#"><i class="fab fa-x-twitter"></i></a>';
            echo '<a href="#"><i class="fab fa-instagram"></i></a>';
            echo '<a href="#"><i class="fab fa-linkedin"></i></a>';
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    public function form($instance) {
        $location = !empty($instance['location']) ? $instance['location'] : '';
        $email = !empty($instance['email']) ? $instance['email'] : '';
        $phone = !empty($instance['phone']) ? $instance['phone'] : '';
        $show_language = !empty($instance['show_language']) ? $instance['show_language'] : '';
        ?>
        <h4><?php _e('Contact Information:', 'mytheme'); ?></h4>
        <p>
            <label for="<?php echo $this->get_field_id('location'); ?>"><?php _e('Location:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('location'); ?>" name="<?php echo $this->get_field_name('location'); ?>" type="text" value="<?php echo esc_attr($location); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('email'); ?>"><?php _e('Email:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('email'); ?>" name="<?php echo $this->get_field_name('email'); ?>" type="email" value="<?php echo esc_attr($email); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('phone'); ?>"><?php _e('Phone:', 'mytheme'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('phone'); ?>" name="<?php echo $this->get_field_name('phone'); ?>" type="text" value="<?php echo esc_attr($phone); ?>">
        </p>

        <h4><?php _e('Options:', 'mytheme'); ?></h4>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_language, 'on'); ?> id="<?php echo $this->get_field_id('show_language'); ?>" name="<?php echo $this->get_field_name('show_language'); ?>" />
            <label for="<?php echo $this->get_field_id('show_language'); ?>"><?php _e('Show Language Dropdown', 'mytheme'); ?></label>
        </p>

        <h4><?php _e('Social Media Links:', 'mytheme'); ?></h4>
        <?php

        $social_networks = array(
            'facebook' => 'Facebook',
            'twitter' => 'Twitter',
            'instagram' => 'Instagram',
            'linkedin' => 'LinkedIn'
        );

        foreach ($social_networks as $network => $name) {
            $url = !empty($instance[$network]) ? $instance[$network] : '';
            ?>
            <p>
                <label for="<?php echo $this->get_field_id($network); ?>"><?php echo sprintf(__('%s URL:', 'mytheme'), $name); ?></label>
                <input class="widefat" id="<?php echo $this->get_field_id($network); ?>" name="<?php echo $this->get_field_name($network); ?>" type="url" value="<?php echo esc_attr($url); ?>">
            </p>
            <?php
        }
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['location'] = (!empty($new_instance['location'])) ? sanitize_text_field($new_instance['location']) : '';
        $instance['email'] = (!empty($new_instance['email'])) ? sanitize_email($new_instance['email']) : '';
        $instance['phone'] = (!empty($new_instance['phone'])) ? sanitize_text_field($new_instance['phone']) : '';
        $instance['show_language'] = (!empty($new_instance['show_language'])) ? $new_instance['show_language'] : '';

        $social_networks = array('facebook', 'twitter', 'instagram', 'linkedin');
        foreach ($social_networks as $network) {
            $instance[$network] = (!empty($new_instance[$network])) ? esc_url_raw($new_instance[$network]) : '';
        }

        return $instance;
    }
}

// Register widgets
function mytheme_register_custom_widgets() {
    register_widget('Mytheme_Newsletter_Widget');
    register_widget('Mytheme_Quick_Links_Widget');
    register_widget('Mytheme_Social_Widget');
    register_widget('Mytheme_Contact_Widget');
    register_widget('Mytheme_Footer_Bottom_Widget');
    register_widget('Mytheme_Header_Top_Widget');
}
add_action('widgets_init', 'mytheme_register_custom_widgets');
