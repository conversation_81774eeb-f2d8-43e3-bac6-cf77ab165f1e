<?php
/*
Template Name: GGC Login Page
*/

// Handle login form submission
$login_error = '';
if (isset($_POST['login_submit'])) {

    if (!isset($_POST['login_nonce']) || !wp_verify_nonce($_POST['login_nonce'], 'ggc_login_nonce')) {
        wp_die('Security check failed.');
    }

    global $wpdb;
    $email = sanitize_text_field($_POST['email']);

    $user = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM user_registration WHERE email = %s",
        $email
    ));

    if ($user->email) {
        $name = $user->first_name . ' ' . $user->last_name;
        $email = $user->email;
        $password = $user->password;

        $message = "
        Hi {$name},<br><br>

        Here are your login details:<br>
        <strong>Email:</strong> {$email}<br>
        <strong>Password:</strong> {$password}<br><br>

        Log in here to complete or update your business profile:<br>
        <a href='https://goglobalconference.com/ggc-login/'>https://goglobalconference.com/ggc-login/</a><br><br>


        If you have any questions, feel free to reach out.<br><br>
        Email: <a href='mailto:<EMAIL>'><EMAIL></a><br>
        Phone: <a href='tel:8779646222'>************</a><br>

        Best regards,<br>
        Go Global Conference Team
        ";


        // Send email
        $to = $email;
        $subject = 'Welcome to Go Global Conference!';
        $message = $message;
        $headers = array('Content-Type: text/html; charset=UTF-8');

        wp_mail($to, $subject, $message, $headers);
        $login_error = 'Email contianing your login details has been sent.';
    } else {
        $login_error = 'Email not found.';
    }
}

get_header(); ?>

<main class="main">
    <?php get_template_part('template/home/<USER>'); ?>
    <!-- login area -->
    <div class="login-area py-120">
        <div class="container">
            <div class="col-md-5 mx-auto">
                <div class="login-form">
                    <div class="login-header">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/img/hero/conference.png" alt="">
                        <?php if (!empty($login_error)) : ?>
                            <div class="login-error"><?php echo $login_error; ?></div>
                        <?php else : ?>
                            <div class="login-details">Forgotten Password</div>
                        <?php endif; ?>
                    </div>
                    <form method="post" action="<?php echo esc_url($_SERVER['REQUEST_URI']); ?>">
                        <?php wp_nonce_field('ggc_login_nonce', 'login_nonce'); ?>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input name="email" type="email" class="form-control" placeholder="Your Email">
                        </div>
                        <div class="d-flex align-items-center">
                            <button name="login_submit" type="submit" class="theme-btn"><span class="fas fa-sign-in"></span> Email My Password</button>
                        </div>
                    </form>
                    <div class="login-footer">
                        <p class="mt-20"><a href="/ggc-login/">Login</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>