<?php
if (!$_SESSION['ggc_admin']) {
    wp_redirect(home_url('/ggc-login/'));
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['ggc_user_id'])) {
    wp_redirect(home_url('/ggc-login/'));
    exit;
}
?>

<!-- Add DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css">

<style>
    .profile-image {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
    }

    .profile-image-placeholder {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
    }
</style>

<!-- members area -->
<div class="about-area py-2 px-2">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="site-heading mb-5">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="site-title-tagline">Registered Members</span>
                            <h2 class="site-title">
                                Conference <span>Participants</span>
                            </h2>
                        </div>
                        <div>
                            <button type="button" class="theme-btn me-2" data-bs-toggle="modal" data-bs-target="#addParticipantModal">
                                <i class="fa-solid fa-user-plus"></i> Add New Participant
                            </button>
                            <button type="button" class="theme-btn me-2">
                                <i class="fa-solid fa-users"></i> Welcome Reception Pass Only
                            </button>
                        </div>
                    </div>
                </div>
                <?php
                global $wpdb;
                $table_name = 'user_registration';
                $payments_table = 'user_payments';

                // Join user_registration with user_payments using client_id
                $query = "
                    SELECT ur.*, 
                        up.amount, 
                        up.stripe_customer_id, 
                        up.pay_name, 
                        up.pay_phone, 
                        up.created_at AS payment_date
                    FROM $table_name ur
                    LEFT JOIN $payments_table up 
                        ON ur.client_id = up.client_id
                ";
                $results = $wpdb->get_results($query);

                $count = 0;
                foreach ($results as $item) {
                    if (isset($item->agree_terms) && $item->agree_terms == '1') {
                        $count++;
                    }
                    
                }

                echo '<h4 style="text-align: center;">Total Members: ' . count($results) . '</h4>';
                echo '<h5 style="text-align: center;">Completed Registrations: ' . $count . '</h5>';
                
                if ($results) :
                ?>

                    <table id="members-table" class="table table-bordered table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>Profile</th>
                                <th>Contact</th>
                                <th>Name</th>
                                <th>Payment</th>
                                <th>Complete</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row) : ?>
                                <tr>
                                    <td><?php echo $row->id ?></td>
                                    <td><?php echo $row->created_at ?></td>
                                    <td class="text-center">
                                        <?php if (!empty($row->headshot_upload)) : ?>
                                            <img src="<?php echo esc_url($row->headshot_upload); ?>" alt="Profile" class="profile-image">
                                        <?php else : ?>
                                            <div class="profile-image-placeholder">
                                                <i class="fas fa-user"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo esc_html($row->email); ?><br>
                                        <?php
                                        if (!empty($row->contact_phone)) : 
                                            echo esc_html($row->contact_phone);
                                        elseif (!empty($row->pay_phone)) : 
                                            echo esc_html($row->pay_phone);
                                        endif;
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if (!empty($row->first_name) && !empty($row->last_name)) : 
                                            echo esc_html(($row->first_name . ' ' . $row->last_name));
                                        else : 
                                            echo esc_html($row->pay_name);
                                        endif;
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($row->client_id) && !empty($row->amount)) : ?>
                                            <?php echo esc_html($row->amount); ?> USD<br>
                                        <?php else : ?>
                                            No payment<br>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo ($row->agree_terms) ? 'Yes' : 'No'; ?></td>
                                    <td>
                                        <a style="margin: 3px; padding: 5px 10px; border-radius: 5px;" href="/ggc-register-view?id=<?php echo esc_attr($row->client_id); ?>" class="theme-btn btn-admin">Profile</a>

                                        <a style="margin: 3px; padding: 5px 10px; border-radius: 5px;" href="/ggc-register-members-action?mode=send_email&id=<?php echo esc_attr($row->client_id); ?>" class="theme-btn btn-admin2">Send Login Email</a>

                                        <a style="margin: 3px; padding: 5px 10px; border-radius: 5px;" href="/ggc-register-members-action?mode=send_mautic&id=<?php echo esc_attr($row->client_id); ?>" class="theme-btn btn-admin">Mautic</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                <?php else : ?>
                    <div class="alert alert-info" role="alert">
                        No registrations found.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<!-- members area end -->

<!-- Add New Participant Modal -->
<div class="modal fade" id="addParticipantModal" tabindex="-1" aria-labelledby="addParticipantModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addParticipantModalLabel">Add New Participant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addParticipantForm">
                    <div class="mb-3">
                        <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="firstName" name="firstName" required>
                    </div>
                    <div class="mb-3">
                        <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="lastName" name="lastName" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number (Optional)</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>
                    <div id="modalMessage"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveParticipant">Add Participant</button>
            </div>
        </div>
    </div>
</div>

<!-- Add DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>

<script>
    jQuery(document).ready(function($) {
        $('#members-table').DataTable({
            responsive: true,
            pageLength: 50,
            order: [
                [0, 'desc']
            ]
        });

        // Handle Add Participant form submission
        $('#saveParticipant').on('click', function() {
            const form = $('#addParticipantForm')[0];

            // Check form validity
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const firstName = $('#firstName').val().trim();
            const lastName = $('#lastName').val().trim();
            const email = $('#email').val().trim();
            const phone = $('#phone').val().trim();

            // Disable button and show loading
            $(this).prop('disabled', true).text('Adding...');
            $('#modalMessage').html('');

            // Send AJAX request
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'add_new_participant',
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    phone: phone,
                    nonce: '<?php echo wp_create_nonce('add_participant_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $('#modalMessage').html('<div class="alert alert-success">' + response.data.message + '</div>');
                        $('#addParticipantForm')[0].reset();

                        // Reload the page after 2 seconds to show the new participant
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        $('#modalMessage').html('<div class="alert alert-danger">' + response.data.message + '</div>');
                    }
                },
                error: function() {
                    $('#modalMessage').html('<div class="alert alert-danger">An error occurred. Please try again.</div>');
                },
                complete: function() {
                    $('#saveParticipant').prop('disabled', false).text('Add Participant');
                }
            });
        });

        // Reset form when modal is closed
        $('#addParticipantModal').on('hidden.bs.modal', function() {
            $('#addParticipantForm')[0].reset();
            $('#modalMessage').html('');
            $('#saveParticipant').prop('disabled', false).text('Add Participant');
        });
    });
</script>