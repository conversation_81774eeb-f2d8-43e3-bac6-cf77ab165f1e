<?php
// Step 0: Set credentials and config
$clientId = '3_4eqwsqat994w88ss848wssocg0g88sgkc00c8s8884sow04kcc';
$clientSecret = '4e1l6l603h2ckscg0c00gg00o4kkgsocgo0cc8woowk4sgsw80';
$mauticBaseUrl = 'https://crm.goglobalconference.com';

$segmentMauticPaymentRecieved = 106;
$segmentMauticRegistered = 104;

$call_origin = get_query_var('call_origin');
if ($call_origin === 'paid') {
    $segmentId = $segmentMauticPaymentRecieved;
} else {
    $segmentId = $segmentMauticRegistered;
}

// Step 1: Get Access Token
$tokenUrl = $mauticBaseUrl . '/oauth/v2/token';
$postFields = [
    'grant_type' => 'client_credentials',
    'client_id' => $clientId,
    'client_secret' => $clientSecret,
];

$ch = curl_init($tokenUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postFields));
$tokenResponse = curl_exec($ch);
curl_close($ch);

$tokenData = json_decode($tokenResponse, true);
$accessToken = $tokenData['access_token'];

// Get the current user ID from session
$user_id = $_SESSION['ggc_user_id'];

// Gathering User Information
if (!empty($user_id)) {

    global $wpdb;

    $table_registration = 'user_registration';
    $table_payments = 'user_payments';

    $query = $wpdb->prepare("
        SELECT ur.*, up.*
        FROM $table_registration ur
        LEFT JOIN $table_payments up ON ur.client_id = up.client_id
        WHERE ur.client_id = %s
    ", $user_id);

    $result = $wpdb->get_row($query); // returns single object row

    if ($result) {
        $email = $result->email;
        $contactData = [
            'email' => $result->email,
            'firstname' => $result->first_name,
            'lastname' => $result->last_name,
            'phone' => $result->contact_phone,
            'company' => $result->company_name,
            'address1' => $result->city_state_zip
        ];
    } else {
        die('No payment record found for this user.');
    }
} else {
    die('No user found.');
}

// Sending information to Mautic
try {

    // Contact is Created
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . '/api/contacts/new');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($contactData));
    $contactResponse = curl_exec($ch);
    $contactData = json_decode($contactResponse, true);

    curl_close($ch);

    $contactId = $contactData['contact']['id'];

    // Add to segment POST /segments/SEGMENT_ID/contact/CONTACT_ID/add
    $post_uri_segment = "/api/segments/$segmentId/contact/$contactId/add";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . $post_uri_segment);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $accessToken,
        'Content-Type: application/json',
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    $segmentResponse = curl_exec($ch);
    $segmentResponse = json_decode($segmentResponse, true);

    curl_close($ch);

    if ($call_origin === 'registered') {
        // Remove from segment POST /segments/SEGMENT_ID/contact/CONTACT_ID/remove

        $removed_segment_id = $segmentMauticPaymentRecieved;

        $post_uri_segment = "/api/segments/$removed_segment_id/contact/$contactId/remove";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $mauticBaseUrl . $post_uri_segment);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json',
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
        $segmentResponse = curl_exec($ch);
        $segmentResponse = json_decode($segmentResponse, true);

        curl_close($ch);
    }

    global $wpdb;

    // Define your table name with prefix
    $table_name = 'user_mautic';

    $data = array(
        'client_id'     => !empty($user_id) ? $user_id : '',
        'contact_id'    => !empty($contactId) ? $contactId : '',
        'action'        => !empty($call_origin) ? $call_origin : '',
        'email'         => !empty($email) ? $email : '',
        'data_user'     => !empty($contactData) ? json_encode($contactData) : '',
        'data_segment'  => !empty($segmentResponse) ? json_encode($segmentResponse) : '',
        'time_sent'     => date('F j, Y g:i A') // Always set current time
    );

    $format = array(
        '%s',  // data_send
        '%s',  // data_send
        '%s',  // data_send
        '%s',  // data_send
        '%s',  // data_send
        '%d',  // data_response
        '%s'   // time_sent
    );

    // Insert into the table
    $wpdb->insert($table_name, $data, $format);
} catch (Exception $e) {
    //die('Failed to create contact.');
}
